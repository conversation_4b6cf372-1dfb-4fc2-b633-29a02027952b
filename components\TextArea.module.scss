@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  border-radius: rem(40);
  background-color: $white-one;
  box-shadow: $shadow-two;
  overflow: hidden;

  .label {
    position: absolute;
    left: rem(30000);
    font-size: $body;
    color: $black-one;
  }

  .errorMessage {
    color: $danger;
  }

  .textareaWrapper {
    position: relative;
    border-radius: rem(40);
    overflow: hidden;

    .textarea {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      min-height: rem(120) !important;
      padding: rem(14) rem(48);
      margin-bottom: rem(48);
      font-size: $body;
      line-height: 1.5;
      color: $black-one;
      background-color: $white-one;
      border: none;
      outline: none;
      resize: none;
      transition: height 0.2s ease;
      box-shadow: $shadow-two;
      scroll-behavior: smooth;
      overflow: hidden;
      border-radius: rem(40);


      
      // Hide scrollbar by default
      &::-webkit-scrollbar {
        display: none; // Prevent scrollbar from showing before atMaxHeight
      }

      &.atMaxHeight {
        overflow: auto; // Enable scrollbar when max height is reached
        max-height: rem(240); // Ensure max-height is enforced

        &::-webkit-scrollbar {
          display: block; // Show scrollbar when atMaxHeight
          width: rem(12); // Your specified width
          height: rem(12); // Your specified height
        }

        &::-webkit-scrollbar-track {
          background-color: transparent; // Transparent track
          margin-block: rem(80);
        }

        &::-webkit-scrollbar-thumb {
          background-color: $warning-hover !important; // Your specified color
          min-height: rem(56); // Use min-height for thumb
          border-radius: rem(4);
          border: rem(2) solid transparent; // For spacing
          filter: none !important; // Prevent browser-applied filters (e.g., darkening)
          opacity: 1 !important; // Prevent opacity changes
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: $warning-hover !important;
          filter: none !important; // Prevent browser-applied filters (e.g., darkening)
          opacity: 1 !important; // Prevent opacity changes
        }

        &::-webkit-scrollbar-button {
          display: none; // Hide arrows
          width: 0;
          height: 0;
        }
      }
    }

      .largeTextarea {
    width: rem(311);
    height: rem(56);

    @include only(largeDesktop) {
      width: rem(514);
      height: rem(56);
    }

    @include only(smallDesktop) {
      width: rem(462);
      height: rem(56);
    }

    @include only(tablet) {
      width: rem(462);
      height: rem(56);
    }

    @include only(extraLargeDesktop) {
      width: rem(514);
      height: rem(56);
    }
  }
  .mediumTextarea {
    width: rem(202);
    height: rem(56);

    @include only(largeDesktop) {
      width: rem(424) !important;
      height: rem(56) !important;
    }
    @include only(smallDesktop) {
      width: rem(381);
      height: rem(56);
    }
    @include only(tablet) {
      width: rem(381);
      height: rem(56);
    }
    @include only(extraLargeDesktop) {
      width: rem(424);
      height: rem(56);
    }
  }

  .largeDashboardTextarea {
    width: rem(267);
    height: rem(56);

    @include only(largeDesktop) {
      width: rem(471);
    }
    @include only(smallDesktop) {
      width: rem(424);
    }
    @include only(tablet) {
      width: rem(424);
    }
  }

  .mediumDashboardTextarea {
    width: rem(240);
    height: rem(48);
    @include only(largeDesktop) {
      width: rem(326);
      height: rem(56);
    }
    @include only(smallDesktop) {
      width: rem(293);
      height: rem(56);
    }
    @include only(tablet) {
      width: rem(293);
      height: rem(56);
    }
  }
  }

  .buttons {
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;

    .submitButton {
      height: rem(40);
      margin-right: rem(16);
      margin-bottom: rem(8);
      background-color: $primary;
      box-shadow: $shadow-one !important;
      // filter: drop-shadow(0px 1px 3px rgba(180, 191, 203, 0.2))
      // drop-shadow(0px 5px 5px rgba(180, 191, 203, 0.17))
      // drop-shadow(0px 11px 7px rgba(180, 191, 203, 0.1))
      // drop-shadow(0px 20px 8px rgba(180, 191, 203, 0.03))
      // drop-shadow(0px 30px 9px rgba(180, 191, 203, 0));
    }

    .submitButton:hover {
      background-color: $primary-hover;
      color: $white-one;
    }
  }

  .textarea::placeholder {
    text-align: center;
  }

  .mirrorDiv {
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    min-height: rem(120);
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    font-size: $body;
    line-height: 1.5;
    box-sizing: border-box;
  }
}

// Firefox and other browsers supporting scrollbar-color
@supports (scrollbar-color: $warning-hover transparent) {
  .textarea.atMaxHeight {
    scrollbar-color: $warning-hover transparent; // Apply only when atMaxHeight
    scrollbar-width: thin; // Use thin to minimize scrollbar, no arrows
  }
}

// Size variants
.xxLarge {
  @include only(largeDesktop) {
    width: rem(1296);
  }
}

.xLarge {
  @include only(largeDesktop) {
    width: rem(954);
    height: rem(56);
  }
}

.xLargeFeed {
  width: rem(793);
  height: rem(56);
}

.large {
  width: rem(311);

  @include only(largeDesktop) {
    width: rem(514);
  }
}
.largeFeed {
  width: rem(471);
}

.medium {
  width: rem(202);

  @include only(largeDesktop) {
    width: rem(424);
  }
}

.mediumFeed {
  width: rem(310);
}

.xxLargeDashboard {
  @include only(largeDesktop) {
    width: rem(1002);
  }
}

.xLargeDashboard {
  @include only(largeDesktop) {
  }
}

.largeDashboard {
  @include only(largeDesktop) {
    width: rem(471);
  }
}

.mediumDashboard {
  width: rem(240);

  @include only(largeDesktop) {
    width: rem(326);
  }
}
