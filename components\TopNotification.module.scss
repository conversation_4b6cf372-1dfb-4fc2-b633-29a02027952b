@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.topNotification {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: rem(48);
  // padding: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  // transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  color: $black-four;

  &.show {
    transform: translateY(0);
  }

  &.success {
    background-color: $white-one;
  }

  &.error {
    background-color: $danger;
  }

  &.warning {
    background-color: $warning;
  }

  &.info {
    background-color: $info;
  }
}

.topNotificationContent {
  flex: 1;
  text-align: center;

}

.closeButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: rem(16);
  background: none;
  border: none;
 color: $black-four;
  font-size: 20px;
  cursor: pointer;
  padding: 0 rem(16);
  &:hover {
    color: $black-one;
  }
}