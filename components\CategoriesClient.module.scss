@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  scroll-behavior: smooth;
}

.container::-webkit-scrollbar {
  width: rem(12);
  height: rem(12);
}
.container::-webkit-scrollbar-track {
  background-color: transparent;
  margin-block: rem(80) !important;
}
.container::-webkit-scrollbar-thumb {
  background-color: $warning-hover;
  height: rem(56);
  border-radius: rem(4);
  margin-right: rem(4);
}
.container::-webkit-scrollbar-thumb:hover {
  background-color: $warning-hover;
}

@supports (scrollbar-color: $warning-hover transparent) {
  .container {
    scrollbar-color: $warning-hover transparent;
    scrollbar-width: auto;
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background-color: $white-four;
  width: 100vw;
  height: 90vh;
  padding-top: rem(48);
  padding-bottom: rem(32);
  padding-right: rem(12);
  pointer-events: auto;
  overflow-y: scroll;
  overflow-x: hidden;
  scroll-behavior: smooth;
  pointer-events: auto;
  cursor: url("/icons/X.png"), auto;
  .mobileSubcategoriesContainer {
    margin-bottom: rem(20);

    .mobileSubcategories {
      background-color: $grey-one;
    }
  }

  .categoriesContainer {
    display: flex;
    flex-direction: column;
    cursor: default;
    @include only(largeDesktop) {
      display: unset;
      width: rem(1440);
      columns: rem(200) 4;
      column-gap: rem(24);
      padding: rem(32) rem(0) rem(32) rem(32);
    }
    @include only(smallDesktop) {
      display: unset;
      width: rem(1080);
      columns: rem(200) 3;
      column-gap: rem(24);
      padding: rem(32) rem(0) rem(32) rem(32);
    }
    @include only(tablet) {
      display: unset;
      width: rem(1080);
      columns: rem(200) 3;
      column-gap: rem(24);
      padding: rem(32) rem(0) rem(32) rem(32);
    }

    .categoriesSection {
      margin-bottom: rem(24);
      break-inside: avoid-column;
      max-width: rem(334);
      .category {
        color: $danger;
        font-weight: 500;
        font-size: $h4;
        margin-bottom: rem(32);
      }

      .subcategoryContainer {
        position: relative;
        z-index: 10;
        display: flex;
        align-items: center;
        background-color: $white-three;
        height: rem(56);
        border-radius: rem(40);
        margin-bottom: rem(12);
        cursor: pointer;

        .subcategory {
          padding-left: rem(18);
          margin: rem(2);
          line-height: 1.7;
        }
      }
      .subcategoryContainer:hover {
        background-color: $white-one;
        .subcategory {
          // color: $danger;
        }
      }

      .showMoreContainer {
        display: flex;
        align-items: center;
        background-color: $white-three;
        height: rem(56);
        border-radius: rem(40);

        .more {
          padding-left: rem(18);
          margin: rem(2);
          line-height: 1.7;
        }
      }
      .showMoreContainer:hover {
        cursor: pointer;
        background-color: $white-one;
      }
    }
  }
  .goBackButtonContainer {
    position: fixed;
    margin-top: rem(48);
    margin-bottom: rem(112);
    align-self: center;
    top: 80%;
    z-index: 5;

    .goBackButton {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: rem(-32);
    }
  }
}
