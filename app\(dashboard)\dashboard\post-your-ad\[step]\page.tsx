import dynamic from "next/dynamic";
import styles from "./styles.module.scss";

// Dynamically import client components with SSR disabled
const PostYourAdClient = dynamic(
  () => import("../components/PostYourAdClient"),
  {
    ssr: false,
  }
);

const SelectACategory = dynamic(() => import("../components/SelectACategory"), {
  ssr: false,
});

const Details = dynamic(() => import("../components/Details"), {
  ssr: false,
});

const Price = dynamic(() => import("../components/Price"), {
  ssr: false,
});

const BankAccountDetails = dynamic(
  () => import("../components/BankAccountDetails"),
  {
    ssr: false,
  }
);

const TitleAndDescription = dynamic(
  () => import("../components/TitleAndDescription"),
  {
    ssr: false,
  }
);

const UploadMedia = dynamic(() => import("../components/UploadMedia"), {
  ssr: false,
});

const Location = dynamic(() => import("../components/Location"), {
  ssr: false,
});

const PromoteYourAd = dynamic(() => import("../components/PromoteYourAd"), {
  ssr: false,
});

const Congratulations = dynamic(() => import("../components/Congratulations"), {
  ssr: false,
});

const ReviewAndSubmit = dynamic(() => import("../components/ReviewAndSubmit"), {
  ssr: false,
});

const SelectNewCategory = dynamic(
  () => import("../components/SelectNewCategory"),
  {
    ssr: false,
  }
);

const PostYourAd = () => {
  const steps = [
    <SelectACategory key="0" />,
    <Details key="1" />,
    <Price key="2" />,
    <BankAccountDetails key="3" />,
    <TitleAndDescription key="4" />,
    <UploadMedia key="5" />,
    <Location key="6" />,
    <PromoteYourAd key="7" />,
    <Congratulations key="8" />,
    <ReviewAndSubmit key="9" />,
    <SelectNewCategory key="10" />,
  ];

  const initialFormData = {
    category: { main: "", subcategory: "" },
    details: { condition: "" },
    price: { pricingOption: "", price: 0 },
    createAccount: { bankName: "", accountHolder: "", accountNumber: "" },
    titleAndDescription: { title: "", description: "" },
    uploadMedia: {
      uploadPhotos: false,
      uploadVideos: false,
      uploadAttachments: false,
    },
    location: { province: "", city: "", suburb: "", customLocation: "" },
    promoteYourAd: { promotionDuration: "" },
  };

  return (
    <div className={styles.container}>
      <div className={styles.form}>
        <PostYourAdClient initialFormData={initialFormData} steps={steps} />
      </div>
    </div>
  );
};

export default PostYourAd;
