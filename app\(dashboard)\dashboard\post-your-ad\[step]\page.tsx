"use client";
import styles from "./styles.module.scss";
import BankAccountDetails from "../components/BankAccountDetails";
import Congratulations from "../components/Congratulations";
import Details from "../components/Details";
import Location from "../components/Location";
import PostYourAdClient from "../components/PostYourAdClient";
import Price from "../components/Price";
import PromoteYourAd from "../components/PromoteYourAd";
import ReviewAndSubmit from "../components/ReviewAndSubmit";
import SelectACategory from "../components/SelectACategory";
import SelectNewCategory from "../components/SelectNewCategory";
import TitleAndDescription from "../components/TitleAndDescription";
import UploadMedia from "../components/UploadMedia";

const PostYourAd = () => {
  // Debug: Check each component individually
  console.log("SelectACategory:", SelectACategory);
  console.log("Details:", Details);
  console.log("Price:", Price);
  console.log("BankAccountDetails:", BankAccountDetails);
  console.log("TitleAndDescription:", TitleAndDescription);
  console.log("UploadMedia:", UploadMedia);
  console.log("Location:", Location);
  console.log("PromoteYourAd:", PromoteYourAd);
  console.log("Congratulations:", Congratulations);
  console.log("ReviewAndSubmit:", ReviewAndSubmit);
  console.log("SelectNewCategory:", SelectNewCategory);

  const steps = [
    SelectACategory ? (
      <SelectACategory key="0" />
    ) : (
      <div key="0">SelectACategory is undefined</div>
    ),
    Details ? <Details key="1" /> : <div key="1">Details is undefined</div>,
    Price ? <Price key="2" /> : <div key="2">Price is undefined</div>,
    BankAccountDetails ? (
      <BankAccountDetails key="3" />
    ) : (
      <div key="3">BankAccountDetails is undefined</div>
    ),
    TitleAndDescription ? (
      <TitleAndDescription key="4" />
    ) : (
      <div key="4">TitleAndDescription is undefined</div>
    ),
    UploadMedia ? (
      <UploadMedia key="5" />
    ) : (
      <div key="5">UploadMedia is undefined</div>
    ),
    Location ? <Location key="6" /> : <div key="6">Location is undefined</div>,
    PromoteYourAd ? (
      <PromoteYourAd key="7" />
    ) : (
      <div key="7">PromoteYourAd is undefined</div>
    ),
    Congratulations ? (
      <Congratulations key="8" />
    ) : (
      <div key="8">Congratulations is undefined</div>
    ),
    ReviewAndSubmit ? (
      <ReviewAndSubmit key="9" />
    ) : (
      <div key="9">ReviewAndSubmit is undefined</div>
    ),
    SelectNewCategory ? (
      <SelectNewCategory key="10" />
    ) : (
      <div key="10">SelectNewCategory is undefined</div>
    ),
  ];

  const initialFormData = {
    category: { main: "", subcategory: "" },
    details: { condition: "" },
    price: { pricingOption: "", price: 0 },
    createAccount: { bankName: "", accountHolder: "", accountNumber: "" },
    titleAndDescription: { title: "", description: "" },
    uploadMedia: {
      uploadPhotos: false,
      uploadVideos: false,
      uploadAttachments: false,
    },
    location: { province: "", city: "", suburb: "", customLocation: "" },
    promoteYourAd: { promotionDuration: "" },
  };

  return (
    <div className={styles.container}>
      <div className={styles.form}>
        <PostYourAdClient initialFormData={initialFormData} steps={steps} />
      </div>
    </div>
  );
};

export default PostYourAd;
