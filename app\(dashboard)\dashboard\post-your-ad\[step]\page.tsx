import dynamic from "next/dynamic";
import styles from "./styles.module.scss";

// Dynamically import client components
const PostYourAdClient = dynamic(
  () => import("../components/PostYourAdClient")
);
const SelectACategory = dynamic(() => import("../components/SelectACategory"));
const Details = dynamic(() => import("../components/Details"));
const Price = dynamic(() => import("../components/Price"));
const BankAccountDetails = dynamic(
  () => import("../components/BankAccountDetails")
);
const TitleAndDescription = dynamic(
  () => import("../components/TitleAndDescription")
);
const UploadMedia = dynamic(() => import("../components/UploadMedia"));
const Location = dynamic(() => import("../components/Location"));
const PromoteYourAd = dynamic(() => import("../components/PromoteYourAd"));
const Congratulations = dynamic(() => import("../components/Congratulations"));
const ReviewAndSubmit = dynamic(() => import("../components/ReviewAndSubmit"));
const SelectNewCategory = dynamic(
  () => import("../components/SelectNewCategory")
);

const PostYourAd = () => {
  const steps = [
    <SelectACategory key="0" />,
    <Details key="1" />,
    <Price key="2" />,
    <BankAccountDetails key="3" />,
    <TitleAndDescription key="4" />,
    <UploadMedia key="5" />,
    <Location key="6" />,
    <PromoteYourAd key="7" />,
    <Congratulations key="8" />,
    <ReviewAndSubmit key="9" />,
    <SelectNewCategory key="10" />,
  ];

  const initialFormData = {
    category: { main: "", subcategory: "" },
    details: { condition: "" },
    price: { pricingOption: "", price: 0 },
    createAccount: { bankName: "", accountHolder: "", accountNumber: "" },
    titleAndDescription: { title: "", description: "" },
    uploadMedia: {
      uploadPhotos: false,
      uploadVideos: false,
      uploadAttachments: false,
    },
    location: { province: "", city: "", suburb: "", customLocation: "" },
    promoteYourAd: { promotionDuration: "" },
  };

  return (
    <div className={styles.container}>
      <div className={styles.form}>
        <PostYourAdClient initialFormData={initialFormData} steps={steps} />
      </div>
    </div>
  );
};

export default PostYourAd;
